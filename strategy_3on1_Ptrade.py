# Ptrade版本 - 集合竞价三合一策略
# 2024/08/01  止损卖出修改为跌破5日均线

import pandas as pd
import numpy as np
import datetime as dt
from datetime import datetime
from datetime import timedelta
import math


def initialize(context):
    # 设置股票池
    g.target_list = []
    g.target_list2 = []
    
    # 一进二
    run_daily(get_stock_list, '9:01')
    run_daily(buy, '09:31')
    run_daily(sell, '11:25')
    run_daily(sell, '14:50')

    # 首版低开
    # run_daily(buy2, '09:27') #9:25分知道开盘价后可以提前下单


# 选股
def get_stock_list(context):
    # 文本日期
    import datetime
    date = get_trading_day()
    # 简化日期处理，直接使用字符串日期
    if isinstance(date, str):
        date_obj = datetime.datetime.strptime(date, '%Y-%m-%d')
    else:
        date_obj = date
    
    # 计算前几个交易日
    prev_date = (date_obj - datetime.timedelta(days=1)).strftime('%Y-%m-%d')
    prev_date_2 = (date_obj - datetime.timedelta(days=2)).strftime('%Y-%m-%d')
    prev_date_3 = (date_obj - datetime.timedelta(days=3)).strftime('%Y-%m-%d')

    # 初始列表
    initial_list = prepare_stock_list(prev_date)
    # 昨日涨停
    hl_list = get_hl_stock(initial_list, prev_date)
    # 前日曾涨停
    hl1_list = get_ever_hl_stock(initial_list, prev_date_2)
    # 前前日曾涨停
    hl2_list = get_ever_hl_stock(initial_list, prev_date_3)
    # 合并 hl1_list 和 hl2_list 为一个集合，用于快速查找需要剔除的元素
    elements_to_remove = set(hl1_list + hl2_list)
    # 使用列表推导式来剔除 hl_list 中存在于 elements_to_remove 集合中的元素
    hl_list = [stock for stock in hl_list if stock not in elements_to_remove]

    g.target_list = hl_list

    # 昨日曾涨停
    h1_list = get_ever_hl_stock2(initial_list, prev_date)
    # 上上个交易日涨停过滤
    elements_to_remove = get_hl_stock(initial_list, prev_date_2)

    # 过滤上上个交易日涨停、曾涨停
    all_list = [stock for stock in h1_list if stock not in elements_to_remove]

    g.target_list2 = all_list


# 交易
def buy(context):
    # 先进行择时判断
    log.info('开始进行择时判断...')
    timing_result = select_timing(context)
    log.info('择时判断结果:' + str(timing_result))

    if not timing_result:
        log.info('今日择时信号不满足，不进行交易')
        return

    log.info('择时信号满足，开始选股...')
    qualified_stocks = []
    gk_stocks=[]
    dk_stocks=[]
    rzq_stocks=[]
    
    date_now = get_trading_day()
    
    # 高开
    for s in g.target_list:
        # 条件一：均价，金额，市值，换手率
        prev_day_data = get_history(1, '1d', ['close', 'volume', 'money'], s, include=False)
        if prev_day_data.empty:
            continue
        avg_price_increase_value = prev_day_data['money'].iloc[0] / prev_day_data['volume'].iloc[0] / prev_day_data['close'].iloc[0] * 1.1 - 1
        if avg_price_increase_value < 0.07 or prev_day_data['money'].iloc[0] < 5.5e8 or prev_day_data['money'].iloc[0] > 20e8 :
            continue
        
        # market_cap 总市值(亿元) > 70亿 流通市值(亿元) < 520亿
        fundamentals_data = get_fundamentals(s, date_now, ['market_cap','circulating_market_cap','turnover_ratio'])
        if fundamentals_data.empty or fundamentals_data['market_cap'].iloc[0] < 70  or fundamentals_data['circulating_market_cap'].iloc[0] > 520 :
            continue

        # 条件二：左压
        zyts = calculate_zyts(s, context)
        volume_data = get_history(zyts, '1d', ['volume'], s, include=False)
        if len(volume_data) < 2 or volume_data['volume'].iloc[-1] <= volume_data['volume'].iloc[:-1].max() * 0.9:
            continue

        # 条件三：高开,开比
        snapshot = get_snapshot(s)
        if s not in snapshot:
            continue
        current_price = snapshot[s]['last_px']
        high_limit = snapshot[s]['up_px']
        
        # 获取集合竞价数据（使用历史数据模拟）
        auction_volume = volume_data['volume'].iloc[-1] * 0.05  # 模拟集合竞价成交量
        if auction_volume / volume_data['volume'].iloc[-1] < 0.03:
            continue
        current_ratio = current_price / (high_limit/1.1)
        if current_ratio<=1 or current_ratio>=1.06:
            continue

        # 如果股票满足所有条件，则添加到列表中
        gk_stocks.append(s)
        qualified_stocks.append(s)

    # 低开
    # 基础信息
    import datetime
    prev_date = (datetime.datetime.strptime(date_now.strftime('%Y-%m-%d'), '%Y-%m-%d') - datetime.timedelta(days=1)).strftime('%Y-%m-%d')

    # 昨日涨停列表
    initial_list = prepare_stock_list2(prev_date)
    hl_list = get_hl_stock(initial_list, prev_date)

    if len(hl_list) != 0:
        # 获取非连板涨停的股票
        ccd = get_continue_count_df(hl_list, prev_date, 10)
        lb_list = list(ccd.index)
        stock_list = [s for s in hl_list if s not in lb_list]

        # 计算相对位置
        rpd = get_relative_position_df(stock_list, prev_date, 60)
        rpd = rpd[rpd['rp'] <= 0.5]
        stock_list = list(rpd.index)

        # 低开
        df = get_price(stock_list, prev_date, '1d', ['close'], 1) if len(stock_list) != 0 else pd.DataFrame()
        if not df.empty:
            open_prices = []
            for s in stock_list:
                snapshot = get_snapshot(s)
                if s in snapshot:
                    open_prices.append(snapshot[s]['open_px'])
                else:
                    open_prices.append(None)
            
            df['open_pct'] = [open_prices[i]/df.loc[stock_list[i], 'close'] if open_prices[i] is not None else None for i in range(len(stock_list))]
            df = df.dropna()
            df = df[(0.955 <= df['open_pct']) & (df['open_pct'] <= 0.97)] #低开越多风险越大，选择3个多点即可
            stock_list = list(df.index)

            for s in stock_list:
                prev_day_data = get_history(1, '1d', ['close', 'volume', 'money'], s, include=False)
                if not prev_day_data.empty and prev_day_data['money'].iloc[0] >= 1e8:
                    dk_stocks.append(s)
                    qualified_stocks.append(s)

    # 弱转强
    for s in g.target_list2:
        # 过滤前面三天涨幅超过28%的票
        price_data = get_history(4, '1d', ['close'], s, include=False)
        if len(price_data) < 4:
            continue
        increase_ratio = (price_data['close'].iloc[-1] - price_data['close'].iloc[0]) / price_data['close'].iloc[0]
        if increase_ratio > 0.28:
            continue

        # 过滤前一日收盘价小于开盘价5%以上的票
        prev_day_data = get_history(1, '1d', ['open', 'close'], s, include=False)
        if len(prev_day_data) < 1:
            continue
        open_close_ratio = (prev_day_data['close'].iloc[0] - prev_day_data['open'].iloc[0]) / prev_day_data['open'].iloc[0]
        if open_close_ratio < -0.05:
            continue

        prev_day_data = get_history(1, '1d', ['close', 'volume','money'], s, include=False)
        if prev_day_data.empty:
            continue
        avg_price_increase_value = prev_day_data['money'].iloc[0] / prev_day_data['volume'].iloc[0] / prev_day_data['close'].iloc[0]  - 1
        if avg_price_increase_value < -0.04 or prev_day_data['money'].iloc[0] < 3e8 or prev_day_data['money'].iloc[0] > 19e8:
            continue
        
        fundamentals_data = get_fundamentals(s, date_now, ['market_cap','circulating_market_cap','turnover_ratio'])
        if fundamentals_data.empty or fundamentals_data['market_cap'].iloc[0] < 70  or fundamentals_data['circulating_market_cap'].iloc[0] > 520 :
            continue

        zyts = calculate_zyts(s, context)
        volume_data = get_history(zyts, '1d', ['volume'], s, include=False)
        if len(volume_data) < 2 or volume_data['volume'].iloc[-1] <= volume_data['volume'].iloc[:-1].max() * 0.9:
            continue

        # 获取集合竞价数据（使用历史数据模拟）
        auction_volume = volume_data['volume'].iloc[-1] * 0.05  # 模拟集合竞价成交量
        if auction_volume / volume_data['volume'].iloc[-1] < 0.03:
            continue
        
        snapshot = get_snapshot(s)
        if s not in snapshot:
            continue
        current_price = snapshot[s]['last_px']
        high_limit = snapshot[s]['up_px']
        current_ratio = current_price / (high_limit/1.1)
        if current_ratio <= 0.98 or current_ratio >= 1.09:
            continue
        rzq_stocks.append(s)
        qualified_stocks.append(s)


    if len(qualified_stocks)>0:
        log.info('———————————————————————————————————')
        log.info('今日选股：'+','.join(qualified_stocks))
        log.info('一进二：'+','.join(gk_stocks))
        log.info('首板低开：'+','.join(dk_stocks))
        log.info('弱转强：'+','.join(rzq_stocks))
        log.info('今日选股：'+','.join(qualified_stocks))
        log.info('———————————————————————————————————')
    else:
        log.info('今日无目标个股')

    if len(qualified_stocks)!=0  and context.portfolio.cash/context.portfolio.total_value>0.3:
        value = context.portfolio.cash / len(qualified_stocks)
        for s in qualified_stocks:
            # 下单
            snapshot = get_snapshot(s)
            if s in snapshot:
                current_price = snapshot[s]['last_px']
                open_price = snapshot[s]['open_px']
                #由于关闭了错误日志，不加这一句，不足一手买入失败也会打印买入，造成日志不准确
                if context.portfolio.cash/current_price>100:
                    order_value(s, value, limit_price=open_price)
                    log.info('买入' + s)
                    log.info('———————————————————————————————————')

# 过滤函数
def filter_new_stock(initial_list, date, days=50):
    import datetime as dt
    d_date = dt.datetime.strptime(date, '%Y-%m-%d').date()
    filtered_list = []
    for stock in initial_list:
        stock_info = get_stock_info(stock)
        if stock_info and 'start_date' in stock_info:
            if d_date - stock_info['start_date'].date() > dt.timedelta(days=days):
                filtered_list.append(stock)
    return filtered_list

def filter_st_stock(initial_list, date):
    # Ptrade中使用get_stock_status来过滤ST股票
    filtered_list = []
    for stock in initial_list:
        try:
            status_info = get_stock_status(stock, 'ST')
            if not status_info:  # 如果不是ST股票
                filtered_list.append(stock)
        except:
            # 如果获取状态失败，保守起见不加入列表
            continue
    return filtered_list

def filter_kcbj_stock(initial_list):
    return [stock for stock in initial_list
    if not stock.startswith('4')
    and not stock.startswith('8')
    # and not stock.startswith('3')
    and not stock.startswith('68')]

def filter_paused_stock(initial_list, date):
    filtered_list = []
    for stock in initial_list:
        try:
            status_info = get_stock_status(stock, 'HALT')
            if not status_info:  # 如果不是停牌股票
                filtered_list.append(stock)
        except:
            # 如果获取状态失败，保守起见不加入列表
            continue
    return filtered_list

# 一字
def filter_extreme_limit_stock(context, stock_list, date):
    tmp = []
    for stock in stock_list:
        df = get_price(stock, date, '1d', ['low','high_limit'], 1)
        if not df.empty and df['low'].iloc[0] < df['high_limit'].iloc[0]:
            tmp.append(stock)
    return tmp

# 每日初始股票池
def prepare_stock_list(date):
    initial_list = get_Ashares(date)
    initial_list = filter_kcbj_stock(initial_list)
    initial_list = filter_new_stock(initial_list, date)
    initial_list = filter_st_stock(initial_list, date)
    initial_list = filter_paused_stock(initial_list, date)
    return initial_list

# 计算左压天数
def calculate_zyts(s, context):
    high_prices = get_history(101, '1d', ['high'], s, include=False)['high']
    if len(high_prices) < 3:
        return 100
    prev_high = high_prices.iloc[-1]
    zyts_0 = 100
    for i in range(2, min(len(high_prices), 100)):
        if high_prices.iloc[-(i+1)] >= prev_high:
            zyts_0 = i-1
            break
    zyts = zyts_0 + 5
    return zyts

# 筛选出某一日涨停的股票
def get_hl_stock(initial_list, date):
    hl_list = []
    for stock in initial_list:
        df = get_price(stock, date, '1d', ['close','high_limit'], 1)
        if not df.empty and df['close'].iloc[0] == df['high_limit'].iloc[0]:
            hl_list.append(stock)
    return hl_list

# 筛选曾涨停
def get_ever_hl_stock(initial_list, date):
    hl_list = []
    for stock in initial_list:
        df = get_price(stock, date, '1d', ['high','high_limit'], 1)
        if not df.empty and df['high'].iloc[0] == df['high_limit'].iloc[0]:
            hl_list.append(stock)
    return hl_list

# 筛选曾涨停
def get_ever_hl_stock2(initial_list, date):
    hl_list = []
    for stock in initial_list:
        df = get_price(stock, date, '1d', ['close','high','high_limit'], 1)
        if not df.empty:
            cd1 = df['high'].iloc[0] == df['high_limit'].iloc[0]
            cd2 = df['close'].iloc[0] != df['high_limit'].iloc[0]
            if cd1 and cd2:
                hl_list.append(stock)
    return hl_list

# 计算涨停数
def get_hl_count_df(hl_list, date, watch_days):
    # 获取watch_days的数据
    result_data = []
    for stock in hl_list:
        df = get_price(stock, date, '1d', ['close','high_limit','low'], watch_days)
        if not df.empty:
            #计算涨停与一字涨停数，一字涨停定义为最低价等于涨停价
            hl_days = (df['close'] == df['high_limit']).sum()
            extreme_hl_days = (df['low'] == df['high_limit']).sum()
            result_data.append({'stock': stock, 'count': hl_days, 'extreme_count': extreme_hl_days})

    #创建df记录
    if result_data:
        df_result = pd.DataFrame(result_data)
        df_result.set_index('stock', inplace=True)
        return df_result
    else:
        return pd.DataFrame(columns=['count', 'extreme_count'])

# 计算连板数
def get_continue_count_df(hl_list, date, watch_days):
    df = pd.DataFrame()
    for d in range(2, watch_days+1):
        HLC = get_hl_count_df(hl_list, date, d)
        if not HLC.empty:
            CHLC = HLC[HLC['count'] == d]
            df = pd.concat([df, CHLC])

    if df.empty:
        return pd.DataFrame()

    stock_list = list(set(df.index))
    ccd = pd.DataFrame()
    for s in stock_list:
        tmp = df.loc[[s]]
        if len(tmp) > 1:
            M = tmp['count'].max()
            tmp = tmp[tmp['count'] == M]
        ccd = pd.concat([ccd, tmp])
    if len(ccd) != 0:
        ccd = ccd.sort_values(by='count', ascending=False)
    return ccd

# 获取股票所属行业
def getStockIndustry(stocks):
    try:
        # 使用get_stock_blocks函数获取股票行业信息
        industry_dict = {}
        for stock in stocks:
            blocks = get_stock_blocks(stock)
            if blocks and 'sw_l1' in blocks:
                industry_dict[stock] = blocks['sw_l1']['industry_name']
        return pd.Series(industry_dict)
    except Exception as e:
        log.info(f"获取行业信息出错: {str(e)}")
        return pd.Series()

# 获取市场宽度
def get_market_breadth(context):
    try:
        log.info("开始计算市场宽度...")
        # 指定日期防止未来数据
        import datetime
        date_now = get_trading_day()
        yesterday = (datetime.datetime.strptime(date_now.strftime('%Y-%m-%d'), '%Y-%m-%d') - datetime.timedelta(days=1)).strftime('%Y-%m-%d')
        log.info(f"使用日期: {yesterday}")

        # 获取初始列表 - 使用中证全指成分股
        stocks = get_index_stocks("000985.SS")
        log.info(f"获取到 {len(stocks)} 只股票")

        # 获取股票价格数据
        count = 1
        close_data = []
        for stock in stocks[:100]:  # 限制数量以提高性能
            try:
                h = get_price(stock, yesterday, '1d', ['close'], count + 20)
                if not h.empty:
                    close_data.append({'stock': stock, 'data': h})
            except:
                continue

        if not close_data:
            log.info("未获取到有效价格数据")
            return ""

        log.info(f"有效数据股票数量: {len(close_data)}")

        # 计算20日均线和偏离程度
        bias_data = []
        for item in close_data:
            stock = item['stock']
            df = item['data']
            if len(df) >= 20:
                # 计算20日均线
                ma20 = df['close'].rolling(window=20).mean().iloc[-1]
                # 计算偏离程度（股价是否高于20日均线）
                current_price = df['close'].iloc[-1]
                is_above_ma = current_price > ma20
                bias_data.append({'stock': stock, 'above_ma': is_above_ma})

        if not bias_data:
            log.info("未计算出有效偏离数据")
            return ""

        # 获取股票所属行业
        industry_series = getStockIndustry([item['stock'] for item in bias_data])
        log.info(f"获取到 {len(industry_series)} 只股票的行业信息")

        # 计算行业偏离比例
        industry_stats = {}
        for item in bias_data:
            stock = item['stock']
            if stock in industry_series:
                industry = industry_series[stock]
                if industry not in industry_stats:
                    industry_stats[industry] = {'total': 0, 'above_ma': 0}
                industry_stats[industry]['total'] += 1
                if item['above_ma']:
                    industry_stats[industry]['above_ma'] += 1

        # 计算比例并找出最高的行业
        industry_ratios = {}
        for industry, stats in industry_stats.items():
            if stats['total'] > 0:
                ratio = (stats['above_ma'] / stats['total']) * 100
                industry_ratios[industry] = ratio

        if not industry_ratios:
            log.info("未计算出行业比例")
            return ""

        # 获取偏离程度最高的行业（仅获取Top1）
        top_industry = max(industry_ratios, key=industry_ratios.get)
        log.info("市场宽度计算结果 - 领先行业Top1:" + top_industry)
        return top_industry
    except Exception as e:
        log.info(f"市场宽度计算失败: {str(e)}")
        # 出错时返回空字符串
        return ""

# 择时判断
def select_timing(context):
    log.info("开始执行择时判断函数...")
    try:
        # 获取市场宽度Top1行业
        top_industry = get_market_breadth(context)
        log.info("获取到的市场宽度领先行业Top1:" + top_industry)

        # 需要监控的行业
        restricted_industries = ["银行", "有色金属", "钢铁", "煤炭"]
        log.info("需要监控的行业:" + str(restricted_industries))

        # 检查Top1行业是否在需要监控的行业中
        is_restricted = False
        for industry in restricted_industries:
            if industry in top_industry:
                is_restricted = True
                log.info(f"Top1行业 '{top_industry}' 包含监控行业 '{industry}'")
                break

        if not is_restricted:
            log.info("Top1行业不在监控行业中，择时条件满足，可以交易")
            return True
        else:
            log.info("Top1行业在监控行业中，择时条件不满足，不进行交易")
            return False
    except Exception as e:
        log.info("择时判断出错:" + str(e))
        # 出错时默认允许交易
        return True

#上午有利润就跑
def sell(context):
    # 基础信息
    current_time = get_trading_day()

    # 获取当前时间
    import datetime
    now = datetime.datetime.now()
    current_time_str = now.strftime('%H:%M:%S')

    # 根据时间执行不同的卖出策略
    if current_time_str == '11:25:00':
        for s in list(context.portfolio.positions.keys()):
            position = get_position(s)
            if position.amount > 0:
                snapshot = get_snapshot(s)
                if s in snapshot:
                    current_price = snapshot[s]['last_px']
                    high_limit = snapshot[s]['up_px']
                    if current_price < high_limit and current_price > position.avg_cost:
                        order_target(s, 0)
                        log.info('止盈卖出 ' + s)
                        log.info('———————————————————————————————————')

    if current_time_str == '14:50:00':
        for s in list(context.portfolio.positions.keys()):
            position = get_position(s)
            if position.amount > 0:
                # 计算5日均线
                close_data2 = get_history(4, '1d', ['close'], s, include=False)
                if not close_data2.empty:
                    M4 = close_data2['close'].mean()
                    snapshot = get_snapshot(s)
                    if s in snapshot:
                        current_price = snapshot[s]['last_px']
                        high_limit = snapshot[s]['up_px']
                        MA5 = (M4*4 + current_price)/5

                        if current_price < high_limit and current_price > position.avg_cost:
                            order_target(s, 0)
                            log.info('止盈卖出 ' + s)
                            log.info('———————————————————————————————————')
                        elif current_price < MA5:
                            order_target(s, 0)
                            log.info('止损卖出 ' + s)
                            log.info('———————————————————————————————————')

# 首版低开策略代码
def filter_new_stock2(initial_list, date, days=250):
    import datetime as dt
    d_date = dt.datetime.strptime(date, '%Y-%m-%d').date()
    filtered_list = []
    for stock in initial_list:
        stock_info = get_stock_info(stock)
        if stock_info and 'start_date' in stock_info:
            if d_date - stock_info['start_date'].date() > dt.timedelta(days=days):
                filtered_list.append(stock)
    return filtered_list

# 每日初始股票池
def prepare_stock_list2(date):
    initial_list = get_Ashares(date)
    initial_list = filter_kcbj_stock(initial_list)
    initial_list = filter_new_stock2(initial_list, date)
    initial_list = filter_st_stock(initial_list, date)
    initial_list = filter_paused_stock(initial_list, date)
    return initial_list

# 计算股票处于一段时间内相对位置
def get_relative_position_df(stock_list, date, watch_days):
    if len(stock_list) != 0:
        result_data = []
        for stock in stock_list:
            try:
                df = get_price(stock, date, '1d', ['high', 'low', 'close'], watch_days)
                if not df.empty and len(df) >= watch_days:
                    close = df['close'].iloc[-1]
                    high = df['high'].max()
                    low = df['low'].min()
                    if high != low:  # 避免除零错误
                        rp = (close - low) / (high - low)
                        result_data.append({'stock': stock, 'rp': rp})
            except:
                continue

        if result_data:
            result_df = pd.DataFrame(result_data)
            result_df.set_index('stock', inplace=True)
            return result_df
        else:
            return pd.DataFrame(columns=['rp'])
    else:
        return pd.DataFrame(columns=['rp'])

def handle_data(context, data):
    pass
