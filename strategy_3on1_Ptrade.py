# Ptrade版本 - 集合竞价三合一策略
#coding:gbk

import pandas as pd
import numpy as np
import datetime as dt
from datetime import datetime
from datetime import timedelta
import math


def initialize(context):
    # 设置股票池
    g.target_list = []
    g.target_list2 = []

    # 一进二
    run_daily(context, get_stock_list, time='9:01')
    run_daily(context, buy, time='09:31')
    run_daily(context, sell_morning, time='11:25')
    run_daily(context, sell_afternoon, time='14:50')

    # 首版低开
    # run_daily(context, buy2, time='09:27') #9:25分知道开盘价后可以提前下单


# 选股
def get_stock_list(context):
    # 文本日期
    date = get_trading_day()
    prev_date = get_trade_days(date, -1, 1)[0]
    date_1 = get_trade_days(date, -2, 1)[0]
    date_2 = get_trade_days(date, -3, 1)[0]

    # 初始列表
    initial_list = prepare_stock_list(prev_date)
    # 昨日涨停
    hl_list = get_hl_stock(initial_list, prev_date)
    # 前日曾涨停
    hl1_list = get_ever_hl_stock(initial_list, date_1)
    # 前前日曾涨停
    hl2_list = get_ever_hl_stock(initial_list, date_2)
    # 合并 hl1_list 和 hl2_list 为一个集合，用于快速查找需要剔除的元素
    elements_to_remove = set(hl1_list + hl2_list)
    # 使用列表推导式来剔除 hl_list 中存在于 elements_to_remove 集合中的元素
    hl_list = [stock for stock in hl_list if stock not in elements_to_remove]

    g.target_list = hl_list

    # 昨日曾涨停
    h1_list = get_ever_hl_stock2(initial_list, prev_date)
    # 上上个交易日涨停过滤
    elements_to_remove = get_hl_stock(initial_list, date_1)

    # 过滤上上个交易日涨停、曾涨停
    all_list = [stock for stock in h1_list if stock not in elements_to_remove]

    g.target_list2 = all_list


# 交易
def buy(context):
    # 先进行择时判断
    log.info('开始进行择时判断...')
    timing_result = select_timing(context)
    log.info('择时判断结果:' + str(timing_result))

    if not timing_result:
        log.info('今日择时信号不满足，不进行交易')
        return

    log.info('择时信号满足，开始选股...')
    qualified_stocks = []
    gk_stocks=[]
    dk_stocks=[]
    rzq_stocks=[]
    
    date_now = get_trading_day()
    prev_date = get_trade_days(date_now, -1, 1)[0]
    
    # 高开
    for s in g.target_list:
        # 条件一：均价，金额，市值，换手率
        prev_day_data = get_history(1, '1d', ['close', 'volume', 'money'], s, end_date=prev_date)
        if prev_day_data.empty:
            continue
        avg_price_increase_value = prev_day_data['money'].iloc[0] / prev_day_data['volume'].iloc[0] / prev_day_data['close'].iloc[0] * 1.1 - 1
        if avg_price_increase_value < 0.07 or prev_day_data['money'].iloc[0] < 5.5e8 or prev_day_data['money'].iloc[0] > 20e8 :
            continue
        
        # market_cap 总市值(亿元) > 70亿 流通市值(亿元) < 520亿
        fundamentals_data = get_fundamentals(s, prev_date, prev_date, ['market_cap','circulating_market_cap','turnover_ratio'])
        if fundamentals_data.empty or fundamentals_data['market_cap'].iloc[0] < 70  or fundamentals_data['circulating_market_cap'].iloc[0] > 520 :
            continue

        # 条件二：左压
        zyts = calculate_zyts(s, context)
        volume_data = get_history(zyts, '1d', ['volume'], s, end_date=prev_date)
        if len(volume_data) < 2 or volume_data['volume'].iloc[-1] <= volume_data['volume'].iloc[:-1].max() * 0.9:
            continue

        # 条件三：高开,开比
        # 获取集合竞价数据 - 使用快照数据替代
        snapshot = get_snapshot([s])
        if not snapshot or s not in snapshot:
            continue
        current_price = snapshot[s]['last_px']
        high_limit = snapshot[s]['up_px']
        current_volume = snapshot[s].get('business_amount', 0)

        # 集合竞价成交量判断 - 必须大于昨日成交量的3%
        if current_volume / volume_data['volume'].iloc[-1] < 0.03:
            continue

        current_ratio = current_price / (high_limit/1.1)
        if current_ratio<=1 or current_ratio>=1.06:
            continue

        # 如果股票满足所有条件，则添加到列表中
        gk_stocks.append(s)
        qualified_stocks.append(s)


    # 低开
    # 基础信息
    prev_date_str = prev_date.strftime('%Y-%m-%d')

    # 昨日涨停列表
    initial_list = prepare_stock_list2(prev_date_str)
    hl_list = get_hl_stock(initial_list, prev_date_str)

    if len(hl_list) != 0:
        # 获取非连板涨停的股票
        ccd = get_continue_count_df(hl_list, prev_date_str, 10)
        lb_list = list(ccd.index)
        stock_list = [s for s in hl_list if s not in lb_list]

        # 计算相对位置
        rpd = get_relative_position_df(stock_list, prev_date_str, 60)
        rpd = rpd[rpd['rp'] <= 0.5]
        stock_list = list(rpd.index)

        # 低开
        df = get_price(stock_list, end_date=prev_date_str, frequency='1d', fields=['close'], count=1) if len(stock_list) != 0 else pd.DataFrame()
        if not df.empty:
            df = df.set_index('code')
            # 获取当前开盘价
            open_prices = {}
            for stock in stock_list:
                snapshot = get_snapshot(stock)
                if stock in snapshot:
                    open_prices[stock] = snapshot[stock]['open_px']
            
            df['open_pct'] = [open_prices.get(s, 0)/df.loc[s, 'close'] if s in open_prices else 0 for s in stock_list]
            df = df[(0.955 <= df['open_pct']) & (df['open_pct'] <= 0.97)] #低开越多风险越大，选择3个多点即可
            stock_list = list(df.index)

            for s in stock_list:
                prev_day_data = get_history(1, '1d', ['close', 'volume', 'money'], s, end_date=prev_date)
                if not prev_day_data.empty and prev_day_data['money'].iloc[0] >= 1e8:
                    dk_stocks.append(s)
                    qualified_stocks.append(s)

    # 弱转强
    for s in g.target_list2:
        # 过滤前面三天涨幅超过28%的票
        price_data = get_history(4, '1d', ['close'], s, end_date=prev_date)
        if len(price_data) < 4:
            continue
        increase_ratio = (price_data['close'].iloc[-1] - price_data['close'].iloc[0]) / price_data['close'].iloc[0]
        if increase_ratio > 0.28:
            continue

        # 过滤前一日收盘价小于开盘价5%以上的票
        prev_day_data = get_history(1, '1d', ['open', 'close'], s, end_date=prev_date)
        if len(prev_day_data) < 1:
            continue
        open_close_ratio = (prev_day_data['close'].iloc[0] - prev_day_data['open'].iloc[0]) / prev_day_data['open'].iloc[0]
        if open_close_ratio < -0.05:
            continue

        prev_day_data = get_history(1, '1d', ['close', 'volume','money'], s, end_date=prev_date)
        if prev_day_data.empty:
            continue
        avg_price_increase_value = prev_day_data['money'].iloc[0] / prev_day_data['volume'].iloc[0] / prev_day_data['close'].iloc[0]  - 1
        if avg_price_increase_value < -0.04 or prev_day_data['money'].iloc[0] < 3e8 or prev_day_data['money'].iloc[0] > 19e8:
            continue
        
        fundamentals_data = get_fundamentals(s, prev_date, prev_date, ['turnover_ratio','market_cap','circulating_market_cap'])
        if fundamentals_data.empty or fundamentals_data['market_cap'].iloc[0] < 70  or fundamentals_data['circulating_market_cap'].iloc[0] > 520 :
            continue

        zyts = calculate_zyts(s, context)
        volume_data = get_history(zyts, '1d', ['volume'], s, end_date=prev_date)
        if len(volume_data) < 2 or volume_data['volume'].iloc[-1] <= volume_data['volume'].iloc[:-1].max() * 0.9:
            continue

        # 获取集合竞价数据 - 使用快照数据替代
        snapshot = get_snapshot(s)
        if s not in snapshot:
            continue
        current_price = snapshot[s]['last_px']
        high_limit = snapshot[s]['up_px']
        
        current_ratio = current_price / (high_limit/1.1)
        if current_ratio <= 0.98 or current_ratio >= 1.09:
            continue
        rzq_stocks.append(s)
        qualified_stocks.append(s)


    if len(qualified_stocks)>0:
        log.info('———————————————————————————————————')
        log.info('今日选股：'+','.join(qualified_stocks))
        log.info('一进二：'+','.join(gk_stocks))
        log.info('首板低开：'+','.join(dk_stocks))
        log.info('弱转强：'+','.join(rzq_stocks))
        log.info('今日选股：'+','.join(qualified_stocks))
        log.info('———————————————————————————————————')
    else:
        log.info('今日无目标个股')


    if len(qualified_stocks)!=0  and context.portfolio.available_cash/context.portfolio.total_value>0.3:
        value = context.portfolio.available_cash / len(qualified_stocks)
        for s in qualified_stocks:
            # 下单
            #由于关闭了错误日志，不加这一句，不足一手买入失败也会打印买入，造成日志不准确
            snapshot = get_snapshot(s)
            if s in snapshot and context.portfolio.available_cash/snapshot[s]['last_px']>100:
                order_value(s, value)
                log.info('买入' + s)
                log.info('———————————————————————————————————')

# 处理日期相关函数
def transform_date(date, date_type):
    if type(date) == str:
        str_date = date
        dt_date = dt.datetime.strptime(date, '%Y-%m-%d')
        d_date = dt_date.date()
    elif type(date) == dt.datetime:
        str_date = date.strftime('%Y-%m-%d')
        dt_date = date
        d_date = dt_date.date()
    elif type(date) == dt.date:
        str_date = date.strftime('%Y-%m-%d')
        dt_date = dt.datetime.strptime(str_date, '%Y-%m-%d')
        d_date = date
    dct = {'str':str_date, 'dt':dt_date, 'd':d_date}
    return dct[date_type]

def get_shifted_date(date, days, days_type='T'):
    #获取上一个自然日
    d_date = transform_date(date, 'd')
    yesterday = d_date + dt.timedelta(-1)
    #移动days个自然日
    if days_type == 'N':
        shifted_date = yesterday + dt.timedelta(days+1)
    #移动days个交易日
    if days_type == 'T':
        all_trade_days = get_all_trades_days()
        all_trade_days = [i.strftime('%Y-%m-%d') for i in all_trade_days]
        #如果上一个自然日是交易日，根据其在交易日列表中的index计算平移后的交易日
        if str(yesterday) in all_trade_days:
            shifted_date = all_trade_days[all_trade_days.index(str(yesterday)) + days + 1]
        #否则，从上一个自然日向前数，先找到最近一个交易日，再开始平移
        else:
            for i in range(100):
                last_trade_date = yesterday - dt.timedelta(i)
                if str(last_trade_date) in all_trade_days:
                    shifted_date = all_trade_days[all_trade_days.index(str(last_trade_date)) + days + 1]
                    break
    return str(shifted_date)

# 过滤函数
def filter_new_stock(initial_list, date, days=50):
    d_date = transform_date(date, 'd')
    filtered_list = []
    for stock in initial_list:
        stock_info = get_stock_info(stock)
        if stock_info and 'start_date' in stock_info:
            if d_date - stock_info['start_date'] > dt.timedelta(days=days):
                filtered_list.append(stock)
    return filtered_list

def filter_st_stock(initial_list, date):
    # Ptrade中使用get_stock_status过滤ST股票
    filtered_list = []
    for stock in initial_list:
        try:
            status_info = get_stock_status(stock, query_type='ST')
            if not status_info or len(status_info) == 0:
                filtered_list.append(stock)
        except:
            filtered_list.append(stock)  # 如果查询失败，保留股票
    return filtered_list

def filter_kcbj_stock(initial_list):
    return [stock for stock in initial_list
    if not stock.startswith('4')
    and not stock.startswith('8')
    # and not stock.startswith('3')  # 保留创业板
    and not stock.startswith('68')]  # 过滤科创板

def filter_paused_stock(initial_list, date):
    filtered_list = []
    for stock in initial_list:
        try:
            status_info = get_stock_status(stock, query_type='HALT')
            if not status_info or len(status_info) == 0:
                filtered_list.append(stock)
        except:
            filtered_list.append(stock)  # 如果查询失败，保留股票
    return filtered_list

# 一字
def filter_extreme_limit_stock(context, stock_list, date):
    tmp = []
    for stock in stock_list:
        df = get_price([stock], end_date=date, frequency='1d', fields=['low','high_limit'], count=1)
        if not df.empty and df['low'].iloc[0] < df['high_limit'].iloc[0]:
            tmp.append(stock)
    return tmp

# 每日初始股票池
def prepare_stock_list(date):
    initial_list = get_Ashares(date)
    initial_list = filter_kcbj_stock(initial_list)
    initial_list = filter_new_stock(initial_list, date)
    initial_list = filter_st_stock(initial_list, date)
    initial_list = filter_paused_stock(initial_list, date)
    return initial_list

# 计算左压天数
def calculate_zyts(s, context):
    high_prices = get_history(101, '1d', ['high'], s)
    if high_prices.empty or len(high_prices) < 2:
        return 100
    prev_high = high_prices['high'].iloc[-1]
    zyts_0 = 100
    for i in range(2, min(len(high_prices), 101)):
        if high_prices['high'].iloc[-(i+1)] >= prev_high:
            zyts_0 = i - 1
            break
    zyts = zyts_0 + 5
    return zyts

# 筛选出某一日涨停的股票
def get_hl_stock(initial_list, date):
    df = get_price(initial_list, end_date=date, frequency='1d', fields=['close','high_limit'], count=1)
    if df.empty:
        return []
    df = df.dropna() #去除停牌
    df = df[df['close'] == df['high_limit']]
    hl_list = list(df['code'].unique())
    return hl_list

# 筛选曾涨停
def get_ever_hl_stock(initial_list, date):
    df = get_price(initial_list, end_date=date, frequency='1d', fields=['high','high_limit'], count=1)
    if df.empty:
        return []
    df = df.dropna() #去除停牌
    df = df[df['high'] == df['high_limit']]
    hl_list = list(df['code'].unique())
    return hl_list

# 筛选曾涨停
def get_ever_hl_stock2(initial_list, date):
    df = get_price(initial_list, end_date=date, frequency='1d', fields=['close','high','high_limit'], count=1)
    if df.empty:
        return []
    df = df.dropna() #去除停牌
    cd1 = df['high'] == df['high_limit']
    cd2 = df['close']!= df['high_limit']
    df = df[cd1 & cd2]
    hl_list = list(df['code'].unique())
    return hl_list

# 计算涨停数
def get_hl_count_df(hl_list, date, watch_days):
    # 获取watch_days的数据
    df = get_price(hl_list, end_date=date, frequency='1d', fields=['close','high_limit','low'], count=watch_days)
    if df.empty:
        return pd.DataFrame()

    #计算涨停与一字涨停数，一字涨停定义为最低价等于涨停价
    hl_count_list = []
    extreme_hl_count_list = []
    for stock in hl_list:
        df_sub = df[df['code'] == stock]
        if df_sub.empty:
            hl_count_list.append(0)
            extreme_hl_count_list.append(0)
            continue
        hl_days = len(df_sub[df_sub['close']==df_sub['high_limit']])
        extreme_hl_days = len(df_sub[df_sub['low']==df_sub['high_limit']])
        hl_count_list.append(hl_days)
        extreme_hl_count_list.append(extreme_hl_days)
    #创建df记录
    result_df = pd.DataFrame(index=hl_list, data={'count':hl_count_list, 'extreme_count':extreme_hl_count_list})
    return result_df

# 计算连板数
def get_continue_count_df(hl_list, date, watch_days):
    df = pd.DataFrame()
    for d in range(2, watch_days+1):
        HLC = get_hl_count_df(hl_list, date, d)
        if not HLC.empty:
            CHLC = HLC[HLC['count'] == d]
            df = pd.concat([df, CHLC])

    if df.empty:
        return pd.DataFrame()

    stock_list = list(set(df.index))
    ccd = pd.DataFrame()
    for s in stock_list:
        tmp = df.loc[[s]] if s in df.index else pd.DataFrame()
        if len(tmp) > 1:
            M = tmp['count'].max()
            tmp = tmp[tmp['count'] == M]
        ccd = pd.concat([ccd, tmp])
    if len(ccd) != 0:
        ccd = ccd.sort_values(by='count', ascending=False)
    return ccd

# 计算昨涨幅
def get_index_increase_ratio(index_code, context):
    # 获取指数昨天和前天的收盘价
    close_prices = get_history(2, '1d', ['close'], index_code)
    if len(close_prices) < 2:
        return 0  # 如果数据不足，返回0
    day_before_yesterday_close = close_prices['close'].iloc[0]
    yesterday_close = close_prices['close'].iloc[1]

    # 计算涨幅
    increase_ratio = (yesterday_close - day_before_yesterday_close) / day_before_yesterday_close
    return increase_ratio

# 获取股票所属行业
def getStockIndustry(stocks):
    try:
        # Ptrade中使用get_stock_blocks获取行业信息
        industry_dict = {}
        for stock in stocks:
            try:
                blocks_info = get_stock_blocks(stock)
                if blocks_info and len(blocks_info) > 0:
                    # 获取申万一级行业
                    for block in blocks_info:
                        if 'sw_l1' in block.get('block_type', '').lower():
                            industry_dict[stock] = block.get('block_name', '')
                            break
                    if stock not in industry_dict:
                        # 如果没有申万一级，取第一个行业
                        industry_dict[stock] = blocks_info[0].get('block_name', '')
            except:
                continue
        return pd.Series(industry_dict)
    except Exception as e:
        log.info(f"获取行业信息出错: {str(e)}")
        return pd.Series()

# 获取市场宽度
def get_market_breadth(context):
    try:
        log.info("开始计算市场宽度...")
        # 指定日期防止未来数据
        yesterday = get_trade_days(get_trading_day(), -1, 1)[0]
        log.info(f"使用日期: {yesterday}")

        # 获取初始列表 - 使用中证全指成分股
        stocks = get_index_stocks("000985.XSHG")
        log.info(f"获取到 {len(stocks)} 只股票")

        # 获取股票价格数据
        count = 1
        h = get_price(
            stocks,
            end_date=yesterday,
            frequency="1d",
            fields=["close"],
            count=count + 20
        )

        if h.empty:
            log.info("获取价格数据失败")
            return ""

        # 处理数据
        h['date'] = pd.to_datetime(h['time']).dt.date
        df_close = h.pivot(index="code", columns="date", values="close").dropna(axis=0)
        log.info(f"有效数据股票数量: {len(df_close)}")

        # 计算20日均线
        df_ma20 = df_close.rolling(window=20, axis=1).mean().iloc[:, -count:]

        # 计算偏离程度（股价是否高于20日均线）
        df_bias = df_close.iloc[:, -count:] > df_ma20

        # 获取股票所属行业
        industry_series = getStockIndustry(list(df_close.index))
        log.info(f"获取到 {len(industry_series)} 只股票的行业信息")

        # 将行业信息添加到df_bias
        df_bias["industry_name"] = industry_series

        # 去除没有行业信息的股票
        df_bias = df_bias.dropna(subset=["industry_name"])
        log.info(f"有效行业信息股票数量: {len(df_bias)}")

        if df_bias.empty:
            log.info("没有有效的行业数据")
            return ""

        # 计算行业偏离比例
        df_ratio = ((df_bias.groupby("industry_name").sum() * 100.0) / df_bias.groupby("industry_name").count()).round()
        log.info(f"行业数量: {len(df_ratio)}")

        # 获取偏离程度最高的行业（仅获取Top1）
        if len(df_ratio.columns) > 0:
            latest_date = df_ratio.columns[-1]
            top_value = df_ratio[latest_date].nlargest(1)
            top_industry = top_value.index.tolist()[0] if len(top_value) > 0 else ""
        else:
            top_industry = ""

        log.info("市场宽度计算结果 - 领先行业Top1:" + str(top_industry))
        return top_industry
    except Exception as e:
        log.info(f"市场宽度计算失败: {str(e)}")
        # 出错时返回空字符串
        return ""

# 择时判断
def select_timing(context):
    log.info("开始执行择时判断函数...")
    try:
        # 获取市场宽度Top1行业
        top_industry = get_market_breadth(context)
        log.info("获取到的市场宽度领先行业Top1:" + str(top_industry))

        # 需要监控的行业
        restricted_industries = ["银行", "有色金属", "钢铁", "煤炭"]
        log.info("需要监控的行业:" + str(restricted_industries))

        # 检查Top1行业是否在需要监控的行业中
        is_restricted = False
        for industry in restricted_industries:
            if industry in top_industry:
                is_restricted = True
                log.info(f"Top1行业 '{top_industry}' 包含监控行业 '{industry}'")
                break

        if not is_restricted:
            log.info("Top1行业不在监控行业中，择时条件满足，可以交易")
            return True
        else:
            log.info("Top1行业在监控行业中，择时条件不满足，不进行交易")
            return False
    except Exception as e:
        log.info("择时判断出错:" + str(e))
        # 出错时默认允许交易
        return True

#上午有利润就跑
def sell_morning(context):
    # 基础信息
    date = get_trading_day()
    prev_date = get_trade_days(date, -1, 1)[0]

    for s in list(context.portfolio.positions.keys()):
        position = get_position(s)
        if position.amount > 0:
            snapshot = get_snapshot(s)
            if s in snapshot:
                current_price = snapshot[s]['last_px']
                high_limit = snapshot[s]['up_px']
                avg_cost = position.avg_cost

                if current_price < high_limit and current_price > 1*avg_cost:
                    order_target(s, 0)
                    stock_info = get_stock_info(s)
                    stock_name = stock_info.get('display_name', s) if stock_info else s
                    log.info('止盈卖出 ' + s + ' ' + stock_name)
                    log.info('———————————————————————————————————')

def sell_afternoon(context):
    # 基础信息
    date = get_trading_day()
    prev_date = get_trade_days(date, -1, 1)[0]

    for s in list(context.portfolio.positions.keys()):
        position = get_position(s)
        if position.amount > 0:
            # 计算5日均线
            close_data2 = get_history(4, '1d', ['close'], s)
            if close_data2.empty or len(close_data2) < 4:
                continue

            M4 = close_data2['close'].mean()
            snapshot = get_snapshot(s)
            if s not in snapshot:
                continue

            current_price = snapshot[s]['last_px']
            MA5 = (M4*4 + current_price)/5
            high_limit = snapshot[s]['up_px']
            avg_cost = position.avg_cost

            if current_price < high_limit and current_price > 1*avg_cost:
                order_target(s, 0)
                stock_info = get_stock_info(s)
                stock_name = stock_info.get('display_name', s) if stock_info else s
                log.info('止盈卖出 ' + s + ' ' + stock_name)
                log.info('———————————————————————————————————')
            elif current_price < MA5:
                order_target(s, 0)
                stock_info = get_stock_info(s)
                stock_name = stock_info.get('display_name', s) if stock_info else s
                log.info('止损卖出 ' + s + ' ' + stock_name)
                log.info('———————————————————————————————————')

# 首版低开策略代码
def filter_new_stock2(initial_list, date, days=250):
    d_date = transform_date(date, 'd')
    filtered_list = []
    for stock in initial_list:
        stock_info = get_stock_info(stock)
        if stock_info and 'start_date' in stock_info:
            if d_date - stock_info['start_date'] > dt.timedelta(days=days):
                filtered_list.append(stock)
    return filtered_list

# 每日初始股票池
def prepare_stock_list2(date):
    initial_list = get_Ashares(date)
    initial_list = filter_kcbj_stock(initial_list)
    initial_list = filter_new_stock2(initial_list, date)
    initial_list = filter_st_stock(initial_list, date)
    initial_list = filter_paused_stock(initial_list, date)
    return initial_list

# 计算股票处于一段时间内相对位置
def get_relative_position_df(stock_list, date, watch_days):
    if len(stock_list) != 0:
        df = get_price(stock_list, end_date=date, fields=['high', 'low', 'close'], count=watch_days)
        if df.empty:
            return pd.DataFrame(columns=['rp'])

        result_data = {}
        for stock in stock_list:
            stock_df = df[df['code'] == stock]
            if not stock_df.empty and len(stock_df) > 0:
                close = stock_df['close'].iloc[-1]
                high = stock_df['high'].max()
                low = stock_df['low'].min()
                if high != low:
                    rp = (close - low) / (high - low)
                    result_data[stock] = rp

        result = pd.DataFrame(index=result_data.keys(), data={'rp': list(result_data.values())})
        return result
    else:
        return pd.DataFrame(columns=['rp'])

def handle_data(context, data):
    pass
